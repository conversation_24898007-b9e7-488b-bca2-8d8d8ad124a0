package com.simonmarkets.oauth.domain.response

import com.simonmarkets.oauth.domain.TokenIssuer

case class AddeparOAuthTokenResponse(
    override val accessToken: String,
    override val refreshToken: String,
    override val tokenType: String,
    override val expiresIn: Int,
    tokenIssuer: TokenIssuer = TokenIssuer.Addepar,
    addeparFirm: String,
    addeparSubdomain: String
) extends OAuthTokenResponse
