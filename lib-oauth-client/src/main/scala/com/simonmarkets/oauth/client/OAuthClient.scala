package com.simonmarkets.oauth.client

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.http.HttpClientConfig
import com.simonmarkets.logging.TraceId
import com.simonmarkets.oauth.domain.response.OAuthTokenResponse
import com.simonmarkets.resteasy.authn.config.SourceConfig
import com.typesafe.config.Config
import pureconfig.generic.FieldCoproductHint

import scala.concurrent.Future
import pureconfig.generic.auto._

case class OAuthClientConfig(
    httpClient: HttpClientConfig,
    apiPrefix: String,
    clientId: String,
    clientSecret: String
)

object OAuthClientConfig {

  implicit val sourceConfigHint: FieldCoproductHint[SourceConfig] = new FieldCoproductHint[SourceConfig](key = "type")

  def apply(config: Config): OAuthClientConfig = pureconfig.loadConfigOrThrow[OAuthClientConfig](config)
}


trait OAuthClient {
  def exchange(authCode: String, redirectUrl: String)(implicit traceId: TraceId, user: UserACL): Future[OAuthTokenResponse]

  def refresh(implicit traceId: TraceId, userACL: UserACL): Future[OAuthTokenResponse]
}
