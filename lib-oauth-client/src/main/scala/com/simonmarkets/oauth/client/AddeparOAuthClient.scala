package com.simonmarkets.oauth.client

import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import akka.http.scaladsl.model.{FormData, Uri}
import akka.stream.Materializer
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.oauth.domain.response.{AddeparOAuthTokenResponse, OAuthTokenResponse}
import io.circe.{Decoder, HCursor}
import com.simonmarkets.oauth.client.AddeparOAuthClient._

import scala.concurrent.{ExecutionContext, Future}

class AddeparOAuthClient(httpClient: FutureHttpClient, baseUrl: String, clientId: String, clientSecret: String)(implicit ec: ExecutionContext, mat: Materializer)
  extends OAuthClient with JsonCodecs with TraceLogging {

  override def exchange(authCode: String, redirectUrl: String)(implicit traceId: TraceId, user: UserACL): Future[AddeparOAuthTokenResponse] = {
    val formData = FormData(
      "client_id" -> clientId,
      "client_secret" -> clientSecret,
      "redirect_uri" -> redirectUrl,
      "code" -> authCode,
      "grant_type" -> "authorization_code"
    )
    val uri = Uri(s"$baseUrl/api/public/oauth2/token")

    httpClient.post[FormData, AddeparOAuthTokenResponse](
      uri = uri,
      data = formData,
    )
  }

  override def refresh(implicit traceId: TraceId, userACL: UserACL): Future[OAuthTokenResponse] = {
  ???
  }
}

object AddeparOAuthClient {
  def apply(config: OAuthClientConfig)
    (implicit ec: ExecutionContext, mat: Materializer, as: ActorSystem): AddeparOAuthClient = {
    val client = new FutureHttpClient(Http(), config.httpClient)
    new AddeparOAuthClient(client, config.apiPrefix, config.clientId, config.clientSecret)
  }

  implicit lazy val addeparOAuthTokenResponseDecoder: Decoder[AddeparOAuthTokenResponse] = (cursor: HCursor) => {
    for {
     accessToken <- cursor.downField("access_token").as[String]
     refreshToken <- cursor.downField("refresh_token").as[String]
     subdomain <- cursor.downField("addepar_subdomain").as[String]
     firm <- cursor.downField("addepar_firm").as[String]
     tokenType <- cursor.downField("token_type").as[String]
     expiresIn <- cursor.downField("expires_in").as[Int]
    } yield AddeparOAuthTokenResponse(
      accessToken = accessToken,
      refreshToken = refreshToken,
      addeparSubdomain = subdomain,
      addeparFirm = firm,
      tokenType = tokenType,
      expiresIn = expiresIn
    )
  }
}
