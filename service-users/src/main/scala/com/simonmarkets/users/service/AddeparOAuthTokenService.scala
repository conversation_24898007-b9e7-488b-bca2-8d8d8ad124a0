package com.simonmarkets.users.service

import akka.http.scaladsl.model.StatusCodes
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.OAuthTokenCapabilities._
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.oauth.client.AddeparOAuthClient
import com.simonmarkets.oauth.domain.request.OAuthTokenRequest
import com.simonmarkets.okta.domain.SimonId
import com.simonmarkets.users.domain.{AccessTokenServiceError, GeneralNotFound, Internal, NotAuthorized, OAuthToken, OAuthTokenAcceptedAccessKeyGenerator, UserNotFound}
import com.simonmarkets.users.repository.OAuthTokenRepository
import com.simonmarkets.users.service.AddeparOAuthTokenService.mapErrorType
import io.scalaland.chimney.dsl.TransformerOps
import zio.{Task, UIO, ZIO}

import java.time.Instant

import scala.concurrent.{ExecutionContext, Future}

class AddeparOAuthTokenService(addeparClient: AddeparOAuthClient, tokenRepo: OAuthTokenRepository)(implicit ec: ExecutionContext) extends OAuthTokenService with TraceLogging {

  override def exchangeAndStoreToken(req: OAuthTokenRequest)(implicit traceId: TraceId, user: UserACL): Future[Boolean] = {
    for {
      tokenResponse <- addeparClient.exchange(req.authCode, req.redirectUrl)
      availableAccessKeys = getAvailableAccessKeysForCapabilities(EditCapabilities, user)
      token = tokenResponse.into[OAuthToken]
        .withFieldConst(_.userId, user.userId)
        .withFieldConst(_.acceptedAccessKeys, Set.empty[String])
        .withFieldConst(_.updatedAt, Instant.now)
        .transform
      tokenWithEntitlements = token.copy(acceptedAccessKeys = OAuthTokenAcceptedAccessKeyGenerator.getAcceptedAccessKeys(token))
      upsert <- tokenRepo.storeToken(tokenWithEntitlements, availableAccessKeys)
    } yield upsert
  }

  override def getStoredToken(implicit traceId: TraceId, user: UserACL): Task[OAuthToken] = {
    val availableAccessKeys = getAvailableAccessKeysForCapabilities(EditCapabilities, user)

    for {
      _ <- ZIO.succeed(log.info(s"Retrieving stored access token for user ${user.userId}"))
      token <- ZIO
        .fromFuture(_ => tokenRepo.retrieveToken(user.userId, availableAccessKeys))
        .flatMapError(mapErrorType("No access token found for user"))
        .someOrFail(UserNotFound(SimonId(user.userId)))
    } yield token
  }
}

object AddeparOAuthTokenService extends TraceLogging {
  private def mapErrorType(message: String)(implicit traceId: TraceId): PartialFunction[Throwable, UIO[AccessTokenServiceError]] = {
    case HttpError(resp, StatusCodes.NotFound) =>
      ZIO.succeed(log.error(s"$message: $resp")).map(_ => GeneralNotFound(message))
    case HttpError(resp, StatusCodes.Unauthorized) =>
      ZIO.succeed(log.error(s"$message: $resp")).map(_ => NotAuthorized(message))
    case other =>
      ZIO.succeed(log.error(s"$message: ${other.getMessage}")).map(_ => Internal(s"$message: ${other.getMessage}"))
  }
}
